@startuml ZTE产品检索数据流架构图
!theme plain
skinparam backgroundColor #FFFFFF
skinparam activityStyle rectangle

title ZTE产品检索问答系统 - 数据流架构图

|用户层|
start
:用户输入问题\n"ZXR10 M6000-8S Plus的配置方法";
note right
  输入数据:
  - text: 用户问题文本
  - history: 对话历史数组
  - rewriteText: 重写建议
  - X-Emp-No: 员工工号
end note

|接入层|
:HTTP请求\nPOST /zte-ibo-acm-productretrieve/faq;
:JSON数据解析;
:请求头提取;

|安全层|
:UAC Token验证;
:员工号验证;
:权限检查;

|预处理层|
:文本清理与标准化;
note right
  处理内容:
  - 去除特殊字符
  - 下划线转空格
  - 格式标准化
end note

if (存在对话历史?) then (是)
  :多轮对话上下文分析;
  :调用意图识别模型;
  :生成重写后的问题;
  note right
    重写示例:
    原问题: "它的配置方法"
    重写后: "ZXR10 M6000-8S Plus的配置方法"
  end note
else (否)
  :保持原始问题;
endif

|语言处理层|
:语言类型检测;
note right
  检测结果:
  - 中文: "zh"
  - 英文: "en"
  - 混合: "mixed"
end note

|向量化层|
:BGE-M3向量化;
:生成1024维向量;
note right
  向量示例:
  [0.123, -0.456, 0.789, ...]
  维度: 1024
  模型: BGE-M3
end note

|实体提取层|
:实体提取(dp_entity);
:ES查询产品实体库;
note right
  查询索引: kg_product_dn20240327
  匹配策略:
  1. 全称产品型号匹配
  2. 简称产品型号匹配  
  3. 后缀匹配
  4. 系列匹配
end note

:返回实体信息;
note right
  实体结果示例:
  {
    "ZXR10 M6000-8S Plus": {
      "product_id": "productZXR10 M6000-8S Plus",
      "series_id": "seriesZXR10 M6000-S"
    }
  }
end note

|决策层|
if (提取到产品实体?) then (否)
  |全局检索路径|
  :全局ES检索;
  :全局Milvus检索;
  :全局KG检索;
else (是)
  |产品检索路径|
  :版本号检测;
  note right
    正则表达式: [vV]\d+(?:\.\d+)*
    示例匹配: V1.0, v2.1.3
  end note
  
  if (检测到版本号?) then (是)
    :版本特定文档检索;
    :KG查询: 产品ID + 版本号;
  else (否)
    :产品全文档检索;
    :KG查询: 产品ID;
    if (找到文档?) then (否)
      :KG查询: 系列ID;
    endif
  endif
  
  :获取文档列表;
  note right
    文档列表示例:
    [
      "ZXR10 M6000-8S Plus配置指南.pdf",
      "ZXR10 M6000-8S Plus用户手册.pdf",
      "ZXR10 M6000-8S Plus故障排除.pdf"
    ]
  end note
  
  :文档范围内ES检索;
  :文档范围内Milvus检索;
  :文档范围内KG检索;
endif

|检索层|
:ES文本检索结果;
note right
  ES检索结果:
  - 精确匹配: 完全匹配的文档片段
  - 模糊匹配: 部分匹配的文档片段
  - 评分: BM25相关性得分
end note

:Milvus向量检索结果;
note right
  Milvus检索结果:
  - 向量相似度: 余弦相似度得分
  - Top-K结果: 默认返回前20个
  - 距离度量: 欧几里得距离
end note

:KG知识图谱检索结果;
note right
  KG检索结果:
  - 实体关系: 产品-文档关系
  - 属性信息: 产品属性和规格
  - 结构化数据: 图谱三元组
end note

|融合层|
:三路召回结果合并;
:结果去重处理;
note right
  去重策略:
  - 基于content字段去重
  - 保留最高得分的重复项
  - 合并相同来源的结果
end note

|重排序层|
:BGE-M3重排序模型;
:计算查询-文档相关性;
:生成最终排序结果;
note right
  重排序结果:
  - 相关性得分: 0.0-1.0
  - 排序后的文档列表
  - 来源标识: ES/Milvus/KG
end note

if (版本检索模式?) then (是)
  :LLM质量判断;
  if (答案质量满足?) then (否)
    :回退到全集检索;
  endif
endif

|生成层|
:构建LLM Prompt;
note right
  Prompt结构:
  - 系统指令
  - 用户问题
  - 参考文档内容
  - 产品实体信息
  - 输出格式要求
end note

:调用NebulaBiz LLM;
:流式生成答案;
note right
  LLM配置:
  - 模型: NebulaBiz
  - temperature: 0
  - top_p: 0.85
  - top_k: 5
  - 流式输出: SSE
end note

|输出层|
:Server-Sent Events流式响应;
:实时返回生成内容;
:记录完整日志;

|用户层|
:用户接收答案;
stop

note bottom
  数据流特点:
  1. 多路并行检索提高召回率
  2. 智能路由减少检索范围
  3. 重排序提升精确度
  4. 流式输出改善用户体验
  5. 全链路日志便于调试优化
end note

@enduml
