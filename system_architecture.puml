@startuml ZTE AgenticRAG System Architecture
!theme vibrant
skinparam backgroundColor #F5F5F5
skinparam component {
    BackgroundColor #FFFFFF
    BorderColor #333333
    BorderThickness 2
}
skinparam database {
    BackgroundColor #E6F2FF
    BorderColor #0066CC
}
skinparam cloud {
    BackgroundColor #FFF4E6
    BorderColor #FF8800
}

title **ZTE AgenticRAG 产品技术问答系统架构图**\n<i>Product Technical Q&A System Architecture</i>\n

' 定义组件样式
skinparam rectangle {
    BackgroundColor #F0F8FF
    BorderColor #4169E1
    BorderThickness 2
    RoundCorner 10
}

' 用户层
actor "用户\n(User)" as User #FFE4B5

' API网关层
rectangle "**API网关层**" as APIGateway {
    component "Flask Server\n(main.py)" as Flask #87CEEB
    component "CORS Handler" as CORS #87CEEB
    component "UAC Token\n验证器" as UAC #FF6B6B
}

' 控制器层
rectangle "**控制器层**" as ControllerLayer {
    component "ZXTech Controller\n(/faq API)" as Controller #98FB98
    component "健康检查\n(/info)" as HealthCheck #98FB98
}

' 服务层
rectangle "**核心服务层**" as ServiceLayer {
    component "ZXTech Service\n(业务编排)" as Service #FFB6C1
    component "查询预处理器" as PreProcessor #FFB6C1
    component "实体提取器\n(产品型号识别)" as EntityExtractor #FFB6C1
    component "文档检索策略\n选择器" as StrategySelector #FFB6C1
}

' 查询重写层
rectangle "**查询重写层**" as RewriteLayer {
    component "Rewrite Model\n(多轮对话理解)" as RewriteModel #DDA0DD
    component "Prompt Manager\n(提示词管理)" as PromptManager #DDA0DD
}

' 检索层
rectangle "**多路检索层**" as RetrieveLayer {
    component "ES检索器\n(精确+模糊)" as ESRetriever #FFE4C1
    component "Milvus检索器\n(向量相似)" as MilvusRetriever #FFE4C1
    component "KG检索器\n(知识图谱)" as KGRetriever #FFE4C1
    component "结果融合器" as ResultMerger #FFE4C1
}

' 重排序层
rectangle "**重排序层**" as RerankLayer {
    component "BGE-M3 Reranker\n(多重相似度计算)" as Reranker #B0E0E6
    component "去重处理器" as Deduplicator #B0E0E6
    component "阈值判断器" as ThresholdJudge #B0E0E6
}

' 生成层
rectangle "**答案生成层**" as GenerationLayer {
    component "LLM调用器\n(NebulaBiz)" as LLMCaller #F0E68C
    component "流式输出\n处理器" as StreamProcessor #F0E68C
}

' 嵌入层
rectangle "**向量嵌入层**" as EmbeddingLayer {
    component "BGE-M3\n嵌入模型" as EmbeddingModel #FFDAB9
}

' 外部存储系统
database "**ElasticSearch**\n文本索引" as ES #E6F2FF
database "**Milvus**\n向量数据库" as Milvus #E6F2FF
database "**Nebula Graph**\n知识图谱" as NebulaGraph #E6F2FF

' 外部服务
cloud "**外部服务**" as ExternalServices {
    component "NebulaBiz LLM\n(中兴定制大模型)" as NebulaBizLLM #FFD700
    component "Apollo Config\n(配置中心)" as Apollo #FFD700
    component "UAC\n(统一认证中心)" as UACService #FFD700
}

' 配置管理
rectangle "**配置管理**" as ConfigManagement {
    component "ZXTech Config\n(系统配置)" as Config #D8BFD8
    component "环境变量\n管理器" as EnvManager #D8BFD8
}

' 工具层
rectangle "**工具支持层**" as UtilsLayer {
    component "日志记录器" as Logger #E0E0E0
    component "HTTP客户端" as HTTPClient #E0E0E0
    component "加密工具" as Encryption #E0E0E0
    component "分词工具\n(PKUSeg/SpaCy)" as Tokenizer #E0E0E0
}

' 连接关系 - 主流程
User --> Flask : HTTP请求
Flask --> UAC : Token验证
Flask --> Controller : 路由转发
Controller --> Service : 业务调用

Service --> PreProcessor : 1.预处理
PreProcessor --> EntityExtractor : 2.实体识别
EntityExtractor --> StrategySelector : 3.策略选择

Service --> RewriteModel : 查询重写
RewriteModel --> PromptManager : 获取提示词
RewriteModel --> NebulaBizLLM : LLM重写

StrategySelector --> ESRetriever : 文本检索
StrategySelector --> MilvusRetriever : 向量检索
StrategySelector --> KGRetriever : 图谱检索

ESRetriever --> ES : 查询
MilvusRetriever --> Milvus : 查询
KGRetriever --> NebulaGraph : 查询

ESRetriever --> ResultMerger : 检索结果
MilvusRetriever --> ResultMerger : 向量结果
KGRetriever --> ResultMerger : 图谱结果

ResultMerger --> Reranker : 4.重排序
Reranker --> Deduplicator : 5.去重
Deduplicator --> ThresholdJudge : 6.阈值判断

ThresholdJudge --> LLMCaller : 7.生成答案
LLMCaller --> NebulaBizLLM : 调用LLM
LLMCaller --> StreamProcessor : 8.流式输出
StreamProcessor --> Controller : 返回结果
Controller --> User : HTTP响应

' 嵌入模型连接
Service --> EmbeddingModel : 获取向量
EmbeddingModel --> MilvusRetriever : 向量查询

' 配置管理连接
Apollo --> Config : 配置同步
Config --> Service : 配置注入
Config --> RewriteModel : 模型配置
Config --> Reranker : 阈值配置
Config --> ESRetriever : ES配置
Config --> MilvusRetriever : Milvus配置
Config --> KGRetriever : KG配置

' 工具支持连接
Logger ..> Service : 日志记录
Logger ..> Controller : 请求日志
HTTPClient ..> NebulaBizLLM : HTTP调用
Encryption ..> UAC : 加密验证
Tokenizer ..> PreProcessor : 文本分词

' 图例
legend right
    |= 颜色 |= 说明 |
    |<#87CEEB> | API网关组件 |
    |<#98FB98> | 控制器组件 |
    |<#FFB6C1> | 核心服务组件 |
    |<#DDA0DD> | 查询重写组件 |
    |<#FFE4C1> | 检索组件 |
    |<#B0E0E6> | 重排序组件 |
    |<#F0E68C> | 生成组件 |
    |<#FFDAB9> | 嵌入组件 |
    |<#E6F2FF> | 数据库系统 |
    |<#FFD700> | 外部服务 |
    |<#D8BFD8> | 配置管理 |
    |<#E0E0E0> | 工具支持 |
    
    == 数据流说明 ==
    实线箭头：主要数据流
    虚线箭头：辅助支持
endlegend

' 添加备注
note bottom of Service
    **核心业务流程**：
    1. 查询预处理与实体识别
    2. 智能路由选择检索策略
    3. 多路召回融合
    4. 重排序与质量控制
    5. LLM生成最终答案
end note

note right of RetrieveLayer
    **三路召回机制**：
    - ES: BM25文本匹配
    - Milvus: 向量相似度
    - KG: 知识图谱关系
    
    支持产品版本感知检索
end note

note left of Reranker
    **多重相似度计算**：
    - 模型原始得分
    - TF-IDF相似度
    - 二元TF-IDF相似度
    - 综合阈值判断
end note

@enduml