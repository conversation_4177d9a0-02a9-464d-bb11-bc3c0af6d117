@startuml ZTE AgenticRAG Simplified Architecture
!theme vibrant
skinparam backgroundColor #FFFFFF
skinparam defaultFontName "Microsoft YaHei"
skinparam defaultFontSize 12
skinparam ArrowColor #2E86C1
skinparam ArrowThickness 2

title **ZTE AgenticRAG 系统简化架构图**

' 定义参与者和系统边界
actorr "用户" as Users  #FFE4B5
boundary "API网关" as API #87CEEB
control "控制器" as Controller #98FB98

' 核心处理组件
participant "业务服务" as Service #FFB6C1
participant "查询重写" as Rewrite #DDA0DD
participant "多路检索" as Retriever #FFE4C1
participant "重排序" as Reranker #B0E0E6
participant "答案生成" as Generator #F0E68C

' 数据存储
database "ElasticSearch" as ES #E6F2FF
database "Milvus向量库" as Milvus #E6F2FF
database "知识图谱" as KG #E6F2FF

' 外部服务
cloud "LLM服务" as LLM #FFD700

' 主要流程
autonumber
User -> API : 发送查询请求
API -> API : UAC Token验证
API -> Controller : 转发请求

Controller -> Service : 调用业务服务
Service -> Service : 实体提取\n(产品型号识别)

alt 需要查询重写
    Service -> Rewrite : 重写查询
    Rewrite -> LLM : 调用LLM重写
    LLM --> Rewrite : 返回重写结果
    Rewrite --> Service : 返回优化查询
end

Service -> Retriever : 执行多路检索

par 并行检索
    Retriever -> ES : BM25文本检索
    ES --> Retriever : 文本匹配结果
else
    Retriever -> Milvus : 向量相似度检索
    Milvus --> Retriever : 向量匹配结果
else
    Retriever -> KG : 知识图谱检索
    KG --> Retriever : 图谱关联结果
end

Retriever -> Retriever : 结果融合
Retriever --> Service : 返回候选文档

Service -> Reranker : 重排序
Reranker -> Reranker : 多维度相似度计算\n去重与阈值判断
Reranker --> Service : 返回TopK文档

Service -> Generator : 生成答案
Generator -> LLM : 调用LLM生成
LLM --> Generator : 返回答案
Generator --> Service : 流式输出

Service --> Controller : 返回结果
Controller --> API : 返回响应
API --> User : 显示答案

' 添加说明
note over Service
    **检索策略选择**
    - 有产品实体：产品特定检索
    - 无产品实体：全局检索
    - 支持版本感知检索
end note

note over Retriever
    **三路召回融合**
    - ES: 关键词匹配
    - Milvus: 语义相似
    - KG: 关系推理
end note

note over Reranker
    **智能重排序**
    - BGE-M3模型评分
    - TF-IDF相似度
    - 内容去重
    - 多阈值控制
end note

@enduml