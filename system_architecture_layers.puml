@startuml ZTE AgenticRAG Layered Architecture
!theme vibrant
skinparam backgroundColor #F8F9FA
skinparam packageStyle rectangle
skinparam package {
    BackgroundColor #FFFFFF
    BorderColor #2C3E50
    BorderThickness 2
    FontSize 14
    FontStyle bold
}

title **ZTE AgenticRAG 分层架构图**\n<i>Layered System Architecture</i>\n

' 表示层
package "**表示层 (Presentation Layer)**" #FFE5E5 {
    [用户界面] #FFCDD2
    [API接口 (/faq)] #FFCDD2
    [健康检查 (/info)] #FFCDD2
}

' 网关层
package "**网关层 (Gateway Layer)**" #E5F3FF {
    [Flask Server] #BBDEFB
    [CORS处理] #BBDEFB
    [UAC认证] #BBDEFB
    [请求路由] #BBDEFB
}

' 业务逻辑层
package "**业务逻辑层 (Business Logic Layer)**" #FFF3E5 {
    component "核心服务编排" as BizOrchestration #FFE0B2 {
        [查询预处理]
        [实体识别]
        [策略路由]
        [结果组装]
    }
    
    component "智能处理" as IntelligentProcessing #FFE0B2 {
        [查询重写 (多轮对话)]
        [意图理解]
        [上下文管理]
    }
}

' 检索层
package "**检索层 (Retrieval Layer)**" #E5FFE5 {
    component "多路召回" as MultiRecall #C8E6C9 {
        [ES文本检索]
        [Milvus向量检索]
        [KG图谱检索]
    }
    
    component "融合处理" as Fusion #C8E6C9 {
        [结果合并]
        [去重过滤]
        [相关性排序]
    }
}

' AI模型层
package "**AI模型层 (AI Model Layer)**" #F3E5FF {
    component "嵌入模型" as Embedding #E1BEE7 {
        [BGE-M3 Embedder]
        [向量化处理]
    }
    
    component "重排序模型" as Reranking #E1BEE7 {
        [BGE-M3 Reranker]
        [相似度计算]
        [阈值判断]
    }
    
    component "生成模型" as Generation #E1BEE7 {
        [NebulaBiz LLM]
        [答案生成]
        [流式输出]
    }
}

' 数据访问层
package "**数据访问层 (Data Access Layer)**" #E5E5FF {
    [ES连接器] #C5CAE9
    [Milvus连接器] #C5CAE9
    [Nebula Graph连接器] #C5CAE9
    [HTTP客户端] #C5CAE9
}

' 基础设施层
package "**基础设施层 (Infrastructure Layer)**" #F0F0F0 {
    component "配置管理" as ConfigMgmt #E0E0E0 {
        [Apollo配置中心]
        [环境变量]
        [系统参数]
    }
    
    component "工具支持" as Utils #E0E0E0 {
        [日志系统]
        [加密工具]
        [分词器]
        [监控组件]
    }
    
    component "外部存储" as Storage #E0E0E0 {
        [ElasticSearch集群]
        [Milvus向量数据库]
        [Nebula知识图谱]
    }
}

' 层次关系（从上到下）
[用户界面] -[hidden]down-> [Flask Server]
[Flask Server] -[hidden]down-> BizOrchestration
BizOrchestration -[hidden]down-> MultiRecall
MultiRecall -[hidden]down-> Embedding
Embedding -[hidden]down-> [ES连接器]
[ES连接器] -[hidden]down-> ConfigMgmt

' 数据流关系
[API接口 (/faq)] ..> [Flask Server] : HTTP
[Flask Server] ..> [UAC认证] : 验证
[Flask Server] ..> BizOrchestration : 调用
BizOrchestration ..> IntelligentProcessing : 协作
BizOrchestration ..> MultiRecall : 检索
MultiRecall ..> Fusion : 融合
IntelligentProcessing ..> Generation : LLM调用
MultiRecall ..> Embedding : 向量化
Fusion ..> Reranking : 重排序
Reranking ..> Generation : 生成
[ES文本检索] ..> [ES连接器] : 查询
[Milvus向量检索] ..> [Milvus连接器] : 查询
[KG图谱检索] ..> [Nebula Graph连接器] : 查询
Generation ..> [HTTP客户端] : API调用
ConfigMgmt ..> BizOrchestration : 配置注入
Utils ..> BizOrchestration : 支持服务

' 添加说明
note right of BizOrchestration
    **核心业务流程**
    1. 接收用户查询
    2. 实体识别与提取
    3. 选择检索策略
    4. 执行多路检索
    5. 结果融合与排序
    6. 生成最终答案
end note

note bottom of Storage
    **数据存储说明**
    - ES: 产品文档全文索引
    - Milvus: 文档向量表示
    - Nebula: 产品-文档知识图谱
end note

legend right
    |= 层次 |= 职责 |
    | 表示层 | 用户交互接口 |
    | 网关层 | 请求处理与认证 |
    | 业务逻辑层 | 核心业务流程编排 |
    | 检索层 | 多源数据检索与融合 |
    | AI模型层 | 智能处理与生成 |
    | 数据访问层 | 数据源连接管理 |
    | 基础设施层 | 底层支撑服务 |
endlegend

@enduml