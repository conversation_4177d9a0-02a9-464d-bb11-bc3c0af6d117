@startuml ZTE Product Retrieval Technical Components Architecture
!theme plain

' 在 plain 主题后强制覆盖字体设置
!define FONTNAME "Microsoft YaHei"
skinparam defaultFontName FONTNAME
skinparam defaultFontSize 12

' 为每个元素类型单独设置字体
skinparam actorFontName FONTNAME
skinparam participantFontName FONTNAME
skinparam agentFontName FONTNAME
skinparam componentFontName FONTNAME
skinparam interfaceFontName FONTNAME
skinparam nodeFontName FONTNAME
skinparam packageFontName FONTNAME
skinparam rectangleFontName FONTNAME
skinparam folderFontName FONTNAME
skinparam frameFontName FONTNAME
skinparam cloudFontName FONTNAME
skinparam databaseFontName FONTNAME
skinparam storageFontName FONTNAME
skinparam boundaryFontName FONTNAME
skinparam controlFontName FONTNAME
skinparam entityFontName FONTNAME
skinparam cardFontName FONTNAME
skinparam fileFontName FONTNAME
skinparam queueFontName FONTNAME
skinparam stackFontName FONTNAME
skinparam hexagonFontName FONTNAME
skinparam personFontName FONTNAME
skinparam usecaseFontName FONTNAME
skinparam classFontName FONTNAME
skinparam objectFontName FONTNAME
skinparam activityFontName FONTNAME
skinparam stateFontName FONTNAME
skinparam legendFontName FONTNAME
skinparam titleFontName FONTNAME
skinparam headerFontName FONTNAME
skinparam footerFontName FONTNAME
skinparam noteFontName FONTNAME

skinparam backgroundColor #FFFFFF
skinparam componentStyle rectangle
skinparam packageStyle rectangle

title **ZTE产品检索问答系统 - 技术组件架构图**

package "应用框架层" {
    [Flask 2.x]
    [Flask-CORS]
    [Blueprint路由]
}

package "业务组件层" {
    [控制器组件]
    [服务组件]
}

note right
  测试中文显示
  如果这里不乱码
  说明问题已解决
end note

@enduml